#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正的递增时间戳隐藏技术验证脚本

验证ABC帧的递增时间戳关系和完整性
"""

import subprocess
import sys
import os

def verify_abc_frames(video_path: str):
    """验证ABC帧的完整性和时间戳关系"""
    print(f"🔍 验证ABC帧完整性: {video_path}")
    
    if not os.path.exists(video_path):
        print(f"❌ 视频文件不存在: {video_path}")
        return False
    
    try:
        # 获取总帧数
        cmd_count = [
            'ffprobe',
            '-v', 'quiet',
            '-select_streams', 'v:0',
            '-count_frames',
            '-show_entries', 'stream=nb_read_frames',
            '-of', 'csv=p=0',
            video_path
        ]
        
        result_count = subprocess.run(cmd_count, capture_output=True, text=True)
        if result_count.returncode == 0:
            total_frames = int(result_count.stdout.strip())
            print(f"   总帧数: {total_frames}")
        else:
            print(f"   ⚠️ 无法获取总帧数")
            total_frames = 0
        
        # 获取详细帧信息
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-show_frames',
            '-select_streams', 'v:0',
            '-of', 'csv=p=0:s=,',
            '-show_entries', 'frame=n,pkt_pts_time,pkt_dts_time,pict_type',
            video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ ffprobe执行失败: {result.stderr}")
            return False
        
        lines = result.stdout.strip().split('\n')
        if not lines or lines[0] == '':
            print(f"❌ 无法获取帧信息")
            return False
        
        actual_frames = len([line for line in lines if line.strip()])
        print(f"   实际解析帧数: {actual_frames}")
        
        if total_frames > 0 and actual_frames != total_frames:
            print(f"   ⚠️ 帧数不匹配，可能存在帧丢失")
        
        # 分析ABC帧分布
        abc_distribution = {'A': 0, 'B': 0, 'C': 0}
        correct_sequences = 0
        total_abc_groups = 0
        
        print(f"\n📊 ABC帧时间戳分析:")
        print(f"   {'帧号':<4} {'类型':<4} {'PTS时间':<12} {'DTS时间':<12} {'帧类型':<6}")
        print(f"   {'-'*45}")
        
        for i, line in enumerate(lines[:15]):  # 分析前15帧
            if line.strip():
                parts = line.split(',')
                if len(parts) >= 4:
                    frame_n = parts[0] if parts[0] != 'N/A' else 'N/A'
                    pts_time = parts[1] if parts[1] != 'N/A' else 'N/A'
                    dts_time = parts[2] if parts[2] != 'N/A' else 'N/A'
                    pict_type = parts[3] if parts[3] != 'N/A' else 'N/A'
                    
                    frame_category = ['A', 'B', 'C'][i % 3]
                    abc_distribution[frame_category] += 1
                    
                    print(f"   {frame_n:<4} {frame_category:<4} {pts_time:<12} {dts_time:<12} {pict_type:<6}")
                    
                    # 检查ABC组的时间戳递增关系
                    if i >= 2 and i % 3 == 2:  # 每个C帧时检查ABC组
                        total_abc_groups += 1
                        try:
                            a_line = lines[i-2].split(',')
                            b_line = lines[i-1].split(',')
                            c_line = parts
                            
                            a_pts = float(a_line[1]) if a_line[1] != 'N/A' else None
                            b_pts = float(b_line[1]) if b_line[1] != 'N/A' else None
                            c_pts = float(c_line[1]) if c_line[1] != 'N/A' else None
                            
                            if a_pts is not None and b_pts is not None and c_pts is not None:
                                if a_pts < b_pts < c_pts:
                                    correct_sequences += 1
                                    interval_ab = (b_pts - a_pts) * 1000000  # 转换为微秒
                                    interval_bc = (c_pts - b_pts) * 1000000
                                    print(f"   ✅ ABC组{total_abc_groups}: A→B({interval_ab:.1f}μs) B→C({interval_bc:.1f}μs)")
                                else:
                                    print(f"   ❌ ABC组{total_abc_groups}: 时间戳顺序错误")
                        except Exception as e:
                            print(f"   ⚠️ ABC组{total_abc_groups}: 解析错误 - {e}")
        
        # 统计结果
        print(f"\n📈 ABC帧分布统计:")
        for frame_type, count in abc_distribution.items():
            percentage = (count / actual_frames * 100) if actual_frames > 0 else 0
            print(f"   {frame_type}帧: {count} 个 ({percentage:.1f}%)")
        
        # 检查是否有缺失的帧类型
        missing_types = [t for t, c in abc_distribution.items() if c == 0]
        if missing_types:
            print(f"   ❌ 缺失帧类型: {', '.join(missing_types)}")
        else:
            print(f"   ✅ 所有ABC帧类型都存在")
        
        # 时间戳递增关系验证
        if total_abc_groups > 0:
            success_rate = (correct_sequences / total_abc_groups) * 100
            print(f"\n🔒 时间戳递增关系验证:")
            print(f"   正确的ABC序列: {correct_sequences}/{total_abc_groups} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                print(f"   ✅ 时间戳隐藏技术修正成功")
            else:
                print(f"   ⚠️ 时间戳关系可能需要进一步调整")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

def main():
    """主函数"""
    print("🔒 修正的递增时间戳隐藏技术验证工具")
    print("=" * 50)
    
    if len(sys.argv) != 2:
        print("用法: python verify_timestamp_fix.py <视频文件路径>")
        print("示例: python verify_timestamp_fix.py atell.mp4")
        sys.exit(1)
    
    video_path = sys.argv[1]
    
    # 检查ffprobe是否可用
    try:
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 错误: 未找到ffprobe工具")
        print("   请确保已安装FFmpeg并添加到系统PATH")
        sys.exit(1)
    
    # 验证ABC帧
    if verify_abc_frames(video_path):
        print(f"\n✅ 验证完成")
        print(f"   如果所有ABC帧都存在且时间戳关系正确，")
        print(f"   说明修正的时间戳隐藏技术工作正常")
    else:
        print(f"\n❌ 验证失败")

if __name__ == "__main__":
    main()
