#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
混合C序列生成演示程序
展示修改后的sjtest.py中的混合C序列生成功能
"""

import random
import time
from typing import List, <PERSON><PERSON>

def demo_mixed_c_sequence_generation():
    """演示混合C序列生成功能"""
    print("🎨 混合C序列生成功能演示")
    print("=" * 50)
    
    # 模拟不同的测试场景
    test_scenarios = [
        {"name": "小规模视频", "total_frames": 150},
        {"name": "中等规模视频", "total_frames": 300},
        {"name": "大规模视频", "total_frames": 600}
    ]
    
    for scenario in test_scenarios:
        print(f"\n📹 场景: {scenario['name']} ({scenario['total_frames']} 帧)")
        print("-" * 40)
        
        # 生成唯一性种子
        unique_seed = int(time.time() * 1000000) % 1000000
        random.seed(unique_seed)
        
        total_frames = scenario['total_frames']
        
        # 随机确定混合比例（15-40%自生成图片，60-85%B视频帧）
        generated_ratio = random.uniform(0.15, 0.40)
        b_frame_ratio = 1.0 - generated_ratio
        
        generated_count = int(total_frames * generated_ratio)
        b_frame_count = total_frames - generated_count
        
        print(f"🎯 混合比例:")
        print(f"   自生成图片: {generated_count} 帧 ({generated_ratio:.1%})")
        print(f"   B视频帧: {b_frame_count} 帧 ({b_frame_ratio:.1%})")
        print(f"   唯一性种子: {unique_seed}")
        
        # 生成混合序列
        sequence_types = generate_demo_sequence(total_frames, generated_count, b_frame_count)
        
        # 分析序列质量
        analyze_sequence_quality(sequence_types)
        
        # 显示分布示例
        print(f"📊 分布示例 (前30帧):")
        display_sequence = []
        for i, frame_type in enumerate(sequence_types[:30]):
            if frame_type == 'GENERATED':
                display_sequence.append('G')
            else:
                display_sequence.append('B')
        
        # 按10帧一行显示
        for i in range(0, len(display_sequence), 10):
            line = display_sequence[i:i+10]
            print(f"   帧 {i+1:3d}-{min(i+10, len(display_sequence)):3d}: {''.join(line)}")

def generate_demo_sequence(total_frames: int, generated_count: int, b_frame_count: int) -> List[str]:
    """生成演示用的混合序列"""
    sequence_types = []
    
    if generated_count == 0:
        sequence_types = ['B_FRAME'] * total_frames
    elif b_frame_count == 0:
        sequence_types = ['GENERATED'] * total_frames
    else:
        max_consecutive_allowed = min(3, total_frames // 10)
        
        if generated_count <= b_frame_count:
            interval = total_frames / generated_count if generated_count > 0 else total_frames
            
            positions = []
            for i in range(generated_count):
                base_pos = int(i * interval)
                offset = random.randint(-int(interval//4), int(interval//4))
                pos = max(0, min(total_frames - 1, base_pos + offset))
                positions.append(pos)
            
            positions = sorted(list(set(positions)))
            
            while len(positions) < generated_count:
                new_pos = random.randint(0, total_frames - 1)
                if new_pos not in positions:
                    positions.append(new_pos)
            
            positions = sorted(positions[:generated_count])
            
            pos_set = set(positions)
            for i in range(total_frames):
                if i in pos_set:
                    sequence_types.append('GENERATED')
                else:
                    sequence_types.append('B_FRAME')
        else:
            interval = total_frames / b_frame_count if b_frame_count > 0 else total_frames
            
            positions = []
            for i in range(b_frame_count):
                base_pos = int(i * interval)
                offset = random.randint(-int(interval//4), int(interval//4))
                pos = max(0, min(total_frames - 1, base_pos + offset))
                positions.append(pos)
            
            positions = sorted(list(set(positions)))
            
            while len(positions) < b_frame_count:
                new_pos = random.randint(0, total_frames - 1)
                if new_pos not in positions:
                    positions.append(new_pos)
            
            positions = sorted(positions[:b_frame_count])
            
            pos_set = set(positions)
            for i in range(total_frames):
                if i in pos_set:
                    sequence_types.append('B_FRAME')
                else:
                    sequence_types.append('GENERATED')
        
        # 后处理限制连续长度
        for _ in range(100):
            consecutive_segments = []
            current_type = sequence_types[0] if sequence_types else None
            current_start = 0
            current_length = 1
            
            for i in range(1, len(sequence_types)):
                if sequence_types[i] == current_type:
                    current_length += 1
                else:
                    if current_length > max_consecutive_allowed:
                        consecutive_segments.append((current_start, current_length, current_type))
                    current_type = sequence_types[i]
                    current_start = i
                    current_length = 1
            
            if current_length > max_consecutive_allowed:
                consecutive_segments.append((current_start, current_length, current_type))
            
            if not consecutive_segments:
                break
            
            if consecutive_segments:
                start, length, seg_type = max(consecutive_segments, key=lambda x: x[1])
                opposite_type = 'B_FRAME' if seg_type == 'GENERATED' else 'GENERATED'
                
                for i in range(len(sequence_types)):
                    if (sequence_types[i] == opposite_type and 
                        not (start <= i < start + length)):
                        swap_pos = start + length // 2
                        sequence_types[i], sequence_types[swap_pos] = sequence_types[swap_pos], sequence_types[i]
                        break
    
    return sequence_types

def analyze_sequence_quality(sequence_types: List[str]):
    """分析序列质量"""
    total_frames = len(sequence_types)
    generated_count = sequence_types.count('GENERATED')
    b_frame_count = sequence_types.count('B_FRAME')
    
    generated_ratio = generated_count / total_frames
    b_frame_ratio = b_frame_count / total_frames
    
    # 检查连续性
    max_consecutive = 0
    consecutive_count = 1
    last_type = sequence_types[0] if sequence_types else None
    
    for frame_type in sequence_types[1:]:
        if frame_type == last_type:
            consecutive_count += 1
            max_consecutive = max(max_consecutive, consecutive_count)
        else:
            consecutive_count = 1
        last_type = frame_type
    
    print(f"✅ 序列质量分析:")
    print(f"   实际比例: 自生成 {generated_ratio:.1%}, B帧 {b_frame_ratio:.1%}")
    print(f"   最大连续帧数: {max_consecutive}")
    
    if max_consecutive <= 3:
        print(f"   🎉 连续性控制: 优秀 (≤3帧)")
    elif max_consecutive <= 5:
        print(f"   ⚠️ 连续性控制: 良好 (≤5帧)")
    else:
        print(f"   ❌ 连续性控制: 需改进 (>{max_consecutive}帧)")
    
    # 检查比例是否在要求范围内
    if 0.15 <= generated_ratio <= 0.40:
        print(f"   ✅ 自生成图片比例: 符合要求 (15%-40%)")
    else:
        print(f"   ❌ 自生成图片比例: 超出范围")
    
    if 0.60 <= b_frame_ratio <= 0.85:
        print(f"   ✅ B视频帧比例: 符合要求 (60%-85%)")
    else:
        print(f"   ❌ B视频帧比例: 超出范围")

def main():
    """主函数"""
    print("🚀 sjtest.py 混合C序列功能演示")
    print("展示修改后的C视频生成逻辑")
    print()
    
    demo_mixed_c_sequence_generation()
    
    print("\n" + "=" * 50)
    print("📋 功能特性总结:")
    print("✅ 混合比例: 自生成图片15-40%, B视频帧60-85%")
    print("✅ 随机化: 每次运行比例和分布都不同")
    print("✅ 防连续: 严格控制连续帧数量(≤3帧)")
    print("✅ 交错分布: 智能分布算法确保均匀交错")
    print("✅ 兼容性: 与现有ABC三路交融序列完全兼容")
    print("✅ 质量保证: 内置验证和质量分析机制")

if __name__ == "__main__":
    main()
