#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频帧分析工具 - 提取完整的帧排序和时间戳信息
"""

import subprocess
import json
import sys

def analyze_video_frames(video_path):
    """分析视频帧信息"""
    try:
        # 使用ffprobe获取详细的帧信息
        cmd = [
            'ffprobe', '-v', 'error', 
            '-select_streams', 'v:0',
            '-show_frames',
            '-show_entries', 'frame=n,pkt_pts_time,pict_type,pkt_size',
            '-of', 'json',
            video_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            print(f"FFprobe执行失败: {result.stderr}")
            return None
            
        # 解析JSON结果
        data = json.loads(result.stdout)
        frames = data.get('frames', [])
        
        print(f"=== 视频帧分析报告 ===")
        print(f"视频文件: {video_path}")
        print(f"总帧数: {len(frames)}")
        print()
        
        # 统计帧类型
        frame_types = {}
        for frame in frames:
            pict_type = frame.get('pict_type', 'Unknown')
            frame_types[pict_type] = frame_types.get(pict_type, 0) + 1
        
        print("帧类型统计:")
        for frame_type, count in sorted(frame_types.items()):
            print(f"  {frame_type}帧: {count}个")
        print()
        
        # 输出完整的帧序列信息
        print("=== 完整帧序列信息 ===")
        print("帧号 | 时间戳(秒) | 帧类型 | 数据大小(字节)")
        print("-" * 50)
        
        for i, frame in enumerate(frames):
            frame_num = i + 1
            pts_time = frame.get('pkt_pts_time', 'N/A')
            pict_type = frame.get('pict_type', 'Unknown')
            pkt_size = frame.get('pkt_size', 'N/A')
            
            # 格式化时间戳
            if pts_time != 'N/A':
                try:
                    pts_time = f"{float(pts_time):.6f}"
                except:
                    pts_time = str(pts_time)
            
            print(f"{frame_num:4d} | {pts_time:>12s} | {pict_type:>6s} | {pkt_size:>12s}")
        
        print()
        print("=== 帧序列模式分析 ===")
        
        # 分析帧序列模式
        sequence = [frame.get('pict_type', 'Unknown') for frame in frames]
        
        # 查找I帧位置
        i_frame_positions = [i+1 for i, frame_type in enumerate(sequence) if frame_type == 'I']
        print(f"I帧位置: {i_frame_positions}")
        
        # 分析前10帧的模式
        if len(sequence) >= 10:
            first_10 = sequence[:10]
            print(f"前10帧模式: {'-'.join(first_10)}")
        
        # 分析第11帧开始的模式（如果有足够的帧）
        if len(sequence) > 10:
            pattern_start = sequence[10:min(30, len(sequence))]
            print(f"第11-30帧模式: {'-'.join(pattern_start)}")
        
        # 检查PBPB交替模式
        if len(sequence) > 10:
            pbpb_start = 10  # 从第11帧开始
            pbpb_pattern = []
            for i in range(pbpb_start, min(pbpb_start + 20, len(sequence))):
                pbpb_pattern.append(sequence[i])
            
            # 检查是否符合PBPB模式
            is_pbpb = True
            for i in range(0, len(pbpb_pattern)-1, 2):
                if i+1 < len(pbpb_pattern):
                    if pbpb_pattern[i] != 'P' or pbpb_pattern[i+1] != 'B':
                        is_pbpb = False
                        break
            
            print(f"第11帧开始PBPB交替模式: {'是' if is_pbpb else '否'}")
        
        return frames
        
    except Exception as e:
        print(f"分析失败: {e}")
        return None

def main():
    video_path = r"C:\Users\<USER>\Desktop\下载的\averes.mp4"
    
    print("开始分析视频帧信息...")
    frames = analyze_video_frames(video_path)
    
    if frames:
        print(f"\n分析完成！共分析了 {len(frames)} 帧")
    else:
        print("分析失败！")

if __name__ == "__main__":
    main()
