#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试ABC三路交融帧生成问题
检查每个步骤的文件生成情况
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

def debug_abc_frame_generation():
    """调试ABC帧生成过程"""
    print("=" * 80)
    print("调试ABC三路交融帧生成问题")
    print("=" * 80)
    
    # 导入相关函数
    from sjtest import (
        extract_video_frames, 
        generate_mixed_c_sequence, 
        create_c_frames_from_mapping,
        create_frame_sequence,
        rearrange_frames_abc
    )
    
    # 测试视频路径
    video_a_path = r"C:\Users\<USER>\Desktop\下载的\a.mp4"
    video_b_path = r"C:\Users\<USER>\Desktop\下载的\666.mp4"
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp(prefix="debug_abc_")
    print(f"📁 临时目录: {temp_dir}")
    
    try:
        # 步骤1: 提取A视频帧
        print(f"\n🎬 步骤1: 提取A视频帧")
        frames_a_dir = os.path.join(temp_dir, 'frames_a')
        success_a, actual_a_frames = extract_video_frames(video_a_path, frames_a_dir, show_progress=True)
        
        if not success_a:
            print(f"❌ A视频帧提取失败")
            return
            
        a_files = list(Path(frames_a_dir).glob('frame_*.jpg')) or list(Path(frames_a_dir).glob('frame_*.png'))
        print(f"✅ A视频帧提取完成: {len(a_files)}个文件")
        
        # 步骤2: 提取B视频帧
        print(f"\n🎬 步骤2: 提取B视频帧")
        frames_b_dir = os.path.join(temp_dir, 'frames_b')
        success_b, actual_b_frames = extract_video_frames(video_b_path, frames_b_dir, max_frames=actual_a_frames, show_progress=True)
        
        if not success_b:
            print(f"❌ B视频帧提取失败")
            return
            
        b_files = list(Path(frames_b_dir).glob('frame_*.jpg')) or list(Path(frames_b_dir).glob('frame_*.png'))
        print(f"✅ B视频帧提取完成: {len(b_files)}个文件")
        
        # 步骤3: 生成C序列映射
        print(f"\n🎨 步骤3: 生成C序列映射")
        c_sequence_mapping = generate_mixed_c_sequence(actual_a_frames, actual_b_frames)
        
        if not c_sequence_mapping:
            print(f"❌ C序列映射生成失败")
            return
            
        print(f"✅ C序列映射生成完成: {len(c_sequence_mapping)}个映射")
        
        # 步骤4: 创建C帧文件
        print(f"\n📁 步骤4: 创建C帧文件")
        frames_c_dir = os.path.join(temp_dir, 'frames_c')
        success_c = create_c_frames_from_mapping(frames_a_dir, frames_b_dir, frames_c_dir, c_sequence_mapping, show_progress=True)
        
        if not success_c:
            print(f"❌ C帧文件创建失败")
            return
            
        c_files = list(Path(frames_c_dir).glob('frame_*.jpg')) or list(Path(frames_c_dir).glob('frame_*.png'))
        print(f"✅ C帧文件创建完成: {len(c_files)}个文件")
        
        # 步骤5: 创建ABC序列
        print(f"\n🔄 步骤5: 创建ABC三路交融序列")
        c_frames_count = len(c_files)
        sequence = create_frame_sequence(actual_a_frames, actual_b_frames, c_frames_count)
        
        print(f"✅ ABC序列创建完成: {len(sequence)}个序列项")
        
        # 分析序列组成
        a_count = sum(1 for _, source, _ in sequence if source == 'A')
        b_count = sum(1 for _, source, _ in sequence if source == 'B')
        c_count = sum(1 for _, source, _ in sequence if source == 'C')
        
        print(f"📊 序列组成分析:")
        print(f"   A帧数量: {a_count}")
        print(f"   B帧数量: {b_count}")
        print(f"   C帧数量: {c_count}")
        print(f"   总序列长度: {len(sequence)}")
        print(f"   预期总长度: {actual_a_frames * 3} (250×3=750)")
        
        # 步骤6: ABC帧重排
        print(f"\n🔄 步骤6: ABC帧重排")
        frames_output_dir = os.path.join(temp_dir, 'frames_output')
        success_rearrange = rearrange_frames_abc(frames_a_dir, frames_b_dir, frames_c_dir, frames_output_dir, sequence, show_progress=True)
        
        if not success_rearrange:
            print(f"❌ ABC帧重排失败")
            return
            
        output_files = list(Path(frames_output_dir).glob('frame_*.jpg')) or list(Path(frames_output_dir).glob('frame_*.png'))
        print(f"✅ ABC帧重排完成: {len(output_files)}个输出文件")
        
        # 最终验证
        print(f"\n📊 最终验证结果:")
        print("-" * 50)
        print(f"A视频帧文件: {len(a_files)}")
        print(f"B视频帧文件: {len(b_files)}")
        print(f"C混合帧文件: {len(c_files)}")
        print(f"ABC序列长度: {len(sequence)}")
        print(f"输出帧文件: {len(output_files)}")
        print(f"预期输出: {actual_a_frames * 3}")
        
        # 检查是否有文件缺失
        expected_output = actual_a_frames * 3
        if len(output_files) != expected_output:
            print(f"⚠️ 输出文件数量不匹配:")
            print(f"   实际: {len(output_files)}")
            print(f"   预期: {expected_output}")
            print(f"   差异: {expected_output - len(output_files)}")
            
            # 检查序列中的文件引用
            missing_files = []
            for i, (_, source, frame_index) in enumerate(sequence):
                if source == 'A':
                    source_dir = frames_a_dir
                elif source == 'B':
                    source_dir = frames_b_dir
                else:
                    source_dir = frames_c_dir
                    
                # 检测文件格式
                source_file_jpg = os.path.join(source_dir, f'frame_{frame_index:04d}.jpg')
                source_file_png = os.path.join(source_dir, f'frame_{frame_index:04d}.png')
                
                if not (os.path.exists(source_file_jpg) or os.path.exists(source_file_png)):
                    missing_files.append((i+1, source, frame_index))
                    
            if missing_files:
                print(f"❌ 发现缺失的源文件:")
                for seq_num, source, frame_idx in missing_files[:10]:  # 只显示前10个
                    print(f"   序列{seq_num}: {source}帧{frame_idx}")
                if len(missing_files) > 10:
                    print(f"   ... 还有{len(missing_files)-10}个缺失文件")
        else:
            print(f"✅ 输出文件数量正确匹配")
            
        print(f"\n📁 调试文件保存在: {temp_dir}")
        print(f"   可以手动检查各个目录中的文件")
        
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 询问是否保留临时文件
        print(f"\n🗑️ 是否删除临时文件? (y/n): ", end="")
        # 暂时保留文件用于调试
        print("保留临时文件用于调试")

if __name__ == "__main__":
    debug_abc_frame_generation()
