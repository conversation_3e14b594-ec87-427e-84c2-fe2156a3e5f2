#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间戳分析脚本 - 验证ABC三路交融的时间戳隐藏效果
"""

import subprocess
import sys

def analyze_video_timestamps(video_path):
    """分析视频的时间戳模式"""
    
    print("=" * 60)
    print("           视频时间戳分析报告")
    print("=" * 60)
    print(f"分析文件: {video_path}")
    print()
    
    # 获取基本信息
    cmd_info = [
        'ffprobe', '-v', 'quiet',
        '-show_entries', 'format=duration,nb_streams',
        '-show_entries', 'stream=nb_frames,r_frame_rate,time_base',
        video_path
    ]
    
    try:
        result_info = subprocess.run(cmd_info, capture_output=True, text=True)
        if result_info.returncode == 0:
            print("📊 基本信息:")
            lines = result_info.stdout.strip().split('\n')
            for line in lines:
                if 'nb_frames=' in line:
                    frames = line.split('=')[1]
                    print(f"   总帧数: {frames}")
                elif 'duration=' in line:
                    duration = float(line.split('=')[1])
                    print(f"   时长: {duration:.3f}秒")
                elif 'r_frame_rate=' in line and '1000000/1' in line:
                    print(f"   帧率: 30fps (理论)")
    except Exception as e:
        print(f"获取基本信息失败: {e}")
    
    print()
    
    # 获取时间戳数据
    cmd_timestamps = [
        'ffprobe', '-v', 'quiet',
        '-select_streams', 'v:0',
        '-show_entries', 'packet=pts_time,dts_time,flags',
        '-of', 'csv=p=0',
        video_path
    ]
    
    try:
        result = subprocess.run(cmd_timestamps, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 时间戳提取失败: {result.stderr}")
            return
        
        lines = result.stdout.strip().split('\n')
        if not lines:
            print("❌ 未获取到时间戳数据")
            return
        
        print("🔍 时间戳模式分析:")
        print("   格式: PTS_TIME,DTS_TIME,FLAGS")
        print("   K__ = 关键帧(I帧), ___ = 普通帧(P帧)")
        print()
        
        # 分析前30帧的模式
        print("📋 前30帧时间戳详情:")
        print("   帧号  PTS_TIME    DTS_TIME    FLAGS  PTS-DTS差值  帧类型")
        print("   " + "-" * 65)
        
        keyframes = []
        abc_pattern = []
        
        for i, line in enumerate(lines[:30]):
            if not line.strip():
                continue
                
            parts = line.split(',')
            if len(parts) >= 3:
                pts_time = float(parts[0])
                dts_time = float(parts[1])
                flags = parts[2]
                
                pts_dts_diff = pts_time - dts_time
                
                # 判断帧类型
                if flags == 'K__':
                    frame_type = "I帧(关键)"
                    keyframes.append(i)
                else:
                    frame_type = "P帧"
                
                # 判断ABC模式
                frame_index_in_group = i % 3
                if frame_index_in_group == 0:
                    abc_type = "A帧(可见)"
                elif frame_index_in_group == 1:
                    abc_type = "B帧(隐藏)"
                else:
                    abc_type = "C帧(隐藏)"
                
                abc_pattern.append(abc_type)
                
                print(f"   {i+1:3d}   {pts_time:8.6f}  {dts_time:8.6f}  {flags:4s}   {pts_dts_diff:+8.6f}  {frame_type}")
        
        print()
        
        # 分析ABC模式
        print("🎯 ABC三路交融模式验证:")
        a_frames = [i for i, t in enumerate(abc_pattern) if 'A帧' in t]
        b_frames = [i for i, t in enumerate(abc_pattern) if 'B帧' in t]
        c_frames = [i for i, t in enumerate(abc_pattern) if 'C帧' in t]
        
        print(f"   A帧位置 (应该可见): {a_frames[:10]}...")
        print(f"   B帧位置 (应该隐藏): {b_frames[:10]}...")
        print(f"   C帧位置 (应该隐藏): {c_frames[:10]}...")
        print()
        
        # 分析时间戳间隔
        print("⏱️ 时间戳间隔分析:")
        
        # 计算A帧的时间戳间隔
        a_frame_times = []
        for i, line in enumerate(lines):
            if i % 3 == 0 and line.strip():  # A帧
                parts = line.split(',')
                if len(parts) >= 1:
                    a_frame_times.append(float(parts[0]))
        
        if len(a_frame_times) >= 2:
            a_intervals = [a_frame_times[i+1] - a_frame_times[i] for i in range(len(a_frame_times)-1)]
            avg_a_interval = sum(a_intervals) / len(a_intervals)
            print(f"   A帧平均间隔: {avg_a_interval:.6f}秒")
            print(f"   理论30fps间隔: {1/30:.6f}秒")
            print(f"   A帧间隔是否正常: {'✅ 是' if abs(avg_a_interval - 1/30) < 0.001 else '❌ 否'}")
        
        # 分析PTS-DTS差值模式
        print()
        print("🔒 时间戳隐藏效果分析:")
        
        pts_dts_diffs = []
        for i, line in enumerate(lines[:90]):  # 分析前90帧(30组ABC)
            if not line.strip():
                continue
            parts = line.split(',')
            if len(parts) >= 2:
                pts_time = float(parts[0])
                dts_time = float(parts[1])
                diff = pts_time - dts_time
                pts_dts_diffs.append((i, diff, i % 3))
        
        # 按ABC分组分析
        a_diffs = [diff for i, diff, group in pts_dts_diffs if group == 0]
        b_diffs = [diff for i, diff, group in pts_dts_diffs if group == 1]
        c_diffs = [diff for i, diff, group in pts_dts_diffs if group == 2]
        
        if a_diffs:
            print(f"   A帧PTS-DTS差值: 平均 {sum(a_diffs)/len(a_diffs):+.6f}秒")
        if b_diffs:
            print(f"   B帧PTS-DTS差值: 平均 {sum(b_diffs)/len(b_diffs):+.6f}秒")
        if c_diffs:
            print(f"   C帧PTS-DTS差值: 平均 {sum(c_diffs)/len(c_diffs):+.6f}秒")
        
        # 判断隐藏效果
        print()
        print("✅ 隐藏技术验证结果:")
        
        if b_diffs and c_diffs:
            b_avg = sum(b_diffs) / len(b_diffs)
            c_avg = sum(c_diffs) / len(c_diffs)
            
            if b_avg > 0 and c_avg > 0:
                print("   ✅ B帧和C帧的PTS > DTS，符合隐藏条件")
                print("   ✅ 时间戳隐藏技术已正确应用")
            else:
                print("   ❌ B帧和C帧的时间戳差值异常")
        
        # 关键帧分析
        print()
        print("🔑 关键帧分布:")
        print(f"   前30帧中的关键帧位置: {keyframes}")
        
        if keyframes:
            keyframe_intervals = []
            for i in range(len(keyframes)-1):
                interval = keyframes[i+1] - keyframes[i]
                keyframe_intervals.append(interval)
            
            if keyframe_intervals:
                avg_keyframe_interval = sum(keyframe_intervals) / len(keyframe_intervals)
                print(f"   关键帧平均间隔: {avg_keyframe_interval:.1f}帧")
        
        print()
        print("=" * 60)
        print("分析完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 时间戳分析失败: {e}")

if __name__ == "__main__":
    video_path = r"C:\Users\<USER>\Desktop\下载的\atell.mp4"
    analyze_video_timestamps(video_path)
