#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的视频帧分析工具 - 提取帧排序和时间戳信息
"""

import subprocess
import json
import sys

def get_frame_types(video_path):
    """获取帧类型信息"""
    cmd = [
        'ffprobe', '-v', 'error', 
        '-select_streams', 'v:0',
        '-show_frames',
        '-show_entries', 'frame=pict_type',
        '-of', 'csv=p=0',
        video_path
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
    if result.returncode != 0:
        return None
    
    return [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]

def get_timestamps(video_path):
    """获取时间戳信息"""
    cmd = [
        'ffprobe', '-v', 'error', 
        '-select_streams', 'v:0',
        '-show_entries', 'packet=pts_time',
        '-of', 'csv=p=0',
        video_path
    ]
    
    result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
    if result.returncode != 0:
        return None
    
    timestamps = []
    for line in result.stdout.strip().split('\n'):
        if line.strip():
            try:
                timestamps.append(float(line.strip()))
            except:
                timestamps.append(0.0)
    
    return timestamps

def analyze_complete_video(video_path):
    """完整分析视频"""
    print("=== 输出视频完整帧分析报告 ===")
    print(f"视频文件: {video_path}")
    print()
    
    # 获取帧类型
    frame_types = get_frame_types(video_path)
    if not frame_types:
        print("错误: 无法获取帧类型信息")
        return
    
    # 获取时间戳
    timestamps = get_timestamps(video_path)
    if not timestamps:
        print("错误: 无法获取时间戳信息")
        return
    
    # 确保数据长度一致
    min_length = min(len(frame_types), len(timestamps))
    frame_types = frame_types[:min_length]
    timestamps = timestamps[:min_length]
    
    print(f"总帧数: {len(frame_types)}")
    
    # 统计帧类型
    frame_stats = {}
    for frame_type in frame_types:
        frame_stats[frame_type] = frame_stats.get(frame_type, 0) + 1
    
    print("\n帧类型统计:")
    for frame_type, count in sorted(frame_stats.items()):
        print(f"  {frame_type}帧: {count}个")
    
    print("\n=== 完整帧序列和时间戳信息 ===")
    print("帧号 | 时间戳(秒) | 帧类型 | 时间间隔(秒)")
    print("-" * 55)
    
    for i in range(len(frame_types)):
        frame_num = i + 1
        timestamp = timestamps[i]
        frame_type = frame_types[i]
        
        # 计算时间间隔
        if i == 0:
            interval = "N/A"
        else:
            interval = f"{timestamp - timestamps[i-1]:.6f}"
        
        print(f"{frame_num:4d} | {timestamp:>10.6f} | {frame_type:>6s} | {interval:>12s}")
    
    print("\n=== 帧序列模式分析 ===")
    
    # 查找I帧位置
    i_frame_positions = [i+1 for i, frame_type in enumerate(frame_types) if frame_type == 'I']
    print(f"I帧位置: {i_frame_positions}")
    
    # 分析前10帧的模式
    if len(frame_types) >= 10:
        first_10 = frame_types[:10]
        print(f"前10帧模式: {'-'.join(first_10)}")
    
    # 分析第11帧开始的模式
    if len(frame_types) > 10:
        pattern_start = frame_types[10:min(30, len(frame_types))]
        print(f"第11-30帧模式: {'-'.join(pattern_start)}")
    
    # 检查PBPB交替模式
    if len(frame_types) > 10:
        pbpb_start = 10
        pbpb_pattern = frame_types[pbpb_start:min(pbpb_start + 20, len(frame_types))]
        
        is_pbpb = True
        for i in range(0, len(pbpb_pattern)-1, 2):
            if i+1 < len(pbpb_pattern):
                if pbpb_pattern[i] != 'P' or pbpb_pattern[i+1] != 'B':
                    is_pbpb = False
                    break
        
        print(f"第11帧开始PBPB交替模式: {'是' if is_pbpb else '否'}")
    
    # 分析时间戳模式
    print("\n=== 时间戳模式分析 ===")
    if len(timestamps) > 1:
        intervals = [timestamps[i] - timestamps[i-1] for i in range(1, len(timestamps))]
        avg_interval = sum(intervals) / len(intervals)
        print(f"平均帧间隔: {avg_interval:.6f}秒")
        print(f"计算帧率: {1/avg_interval:.2f} fps")
        
        # 检查是否有特殊的时间戳模式
        unique_intervals = list(set([round(interval, 6) for interval in intervals]))
        print(f"不同的时间间隔: {sorted(unique_intervals)}")

def main():
    video_path = r"C:\Users\<USER>\Desktop\下载的\averes.mp4"
    analyze_complete_video(video_path)

if __name__ == "__main__":
    main()
