#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细时间戳分析 - 验证ABC三路交融的时间戳隐藏效果
"""

import subprocess
import sys

def detailed_timestamp_analysis(video_path):
    """详细分析视频的时间戳模式"""
    
    print("=" * 80)
    print("                    详细时间戳分析报告")
    print("=" * 80)
    print(f"分析文件: {video_path}")
    print()
    
    # 获取时间戳数据
    cmd_timestamps = [
        'ffprobe', '-v', 'quiet',
        '-select_streams', 'v:0',
        '-show_entries', 'packet=pts_time,dts_time,flags',
        '-of', 'csv=p=0',
        video_path
    ]
    
    try:
        result = subprocess.run(cmd_timestamps, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 时间戳提取失败: {result.stderr}")
            return
        
        lines = result.stdout.strip().split('\n')
        if not lines:
            print("❌ 未获取到时间戳数据")
            return
        
        print("🔍 时间戳隐藏技术验证:")
        print()
        
        # 解析所有时间戳
        timestamps = []
        for i, line in enumerate(lines):
            if not line.strip():
                continue
            parts = line.split(',')
            if len(parts) >= 3:
                pts_time = float(parts[0])
                dts_time = float(parts[1])
                flags = parts[2]
                timestamps.append({
                    'frame': i,
                    'pts': pts_time,
                    'dts': dts_time,
                    'flags': flags,
                    'pts_dts_diff': pts_time - dts_time,
                    'abc_group': i % 3
                })
        
        print(f"📊 总帧数: {len(timestamps)}")
        print()
        
        # 分析ABC模式的时间戳差值
        print("🎯 ABC三路交融时间戳差值分析:")
        print("   理论设计:")
        print("   - A帧 (N%3=0): DTS=PTS，正常显示")
        print("   - B帧 (N%3=1): DTS<PTS，被隐藏")
        print("   - C帧 (N%3=2): DTS<PTS，被隐藏")
        print()
        
        # 按ABC分组统计
        a_frames = [t for t in timestamps if t['abc_group'] == 0]
        b_frames = [t for t in timestamps if t['abc_group'] == 1]
        c_frames = [t for t in timestamps if t['abc_group'] == 2]
        
        print("📈 实际时间戳差值统计:")
        
        if a_frames:
            a_diffs = [t['pts_dts_diff'] for t in a_frames]
            a_avg = sum(a_diffs) / len(a_diffs)
            a_min = min(a_diffs)
            a_max = max(a_diffs)
            print(f"   A帧 ({len(a_frames)}帧): 平均差值 {a_avg:+.6f}秒 (范围: {a_min:+.6f} ~ {a_max:+.6f})")
        
        if b_frames:
            b_diffs = [t['pts_dts_diff'] for t in b_frames]
            b_avg = sum(b_diffs) / len(b_diffs)
            b_min = min(b_diffs)
            b_max = max(b_diffs)
            print(f"   B帧 ({len(b_frames)}帧): 平均差值 {b_avg:+.6f}秒 (范围: {b_min:+.6f} ~ {b_max:+.6f})")
        
        if c_frames:
            c_diffs = [t['pts_dts_diff'] for t in c_frames]
            c_avg = sum(c_diffs) / len(c_diffs)
            c_min = min(c_diffs)
            c_max = max(c_diffs)
            print(f"   C帧 ({len(c_frames)}帧): 平均差值 {c_avg:+.6f}秒 (范围: {c_min:+.6f} ~ {c_max:+.6f})")
        
        print()
        
        # 详细分析前45帧(15组ABC)
        print("📋 前45帧详细时间戳模式 (15组ABC):")
        print("   帧号  ABC类型  PTS_TIME    DTS_TIME    差值      预期效果")
        print("   " + "-" * 70)
        
        for i in range(min(45, len(timestamps))):
            t = timestamps[i]
            abc_type = ['A帧', 'B帧', 'C帧'][t['abc_group']]
            expected = ['可见', '隐藏', '隐藏'][t['abc_group']]
            
            print(f"   {t['frame']+1:3d}   {abc_type}    {t['pts']:8.6f}  {t['dts']:8.6f}  {t['pts_dts_diff']:+8.6f}  {expected}")
        
        print()
        
        # 验证隐藏效果
        print("🔒 时间戳隐藏效果验证:")
        
        # 检查是否符合隐藏条件
        a_properly_timed = sum(1 for t in a_frames if abs(t['pts_dts_diff']) < 0.01)  # A帧应该差值很小
        b_hidden = sum(1 for t in b_frames if t['pts_dts_diff'] > 0.01)  # B帧应该有明显差值
        c_hidden = sum(1 for t in c_frames if t['pts_dts_diff'] > 0.001)  # C帧应该有差值
        
        print(f"   A帧正常时间戳数量: {a_properly_timed}/{len(a_frames)} ({a_properly_timed/len(a_frames)*100:.1f}%)")
        print(f"   B帧隐藏时间戳数量: {b_hidden}/{len(b_frames)} ({b_hidden/len(b_frames)*100:.1f}%)")
        print(f"   C帧隐藏时间戳数量: {c_hidden}/{len(c_frames)} ({c_hidden/len(c_frames)*100:.1f}%)")
        
        print()
        
        # 问题诊断
        print("🔍 问题诊断:")
        
        # 检查A帧是否真的正常
        if a_frames:
            a_avg_diff = sum(t['pts_dts_diff'] for t in a_frames) / len(a_frames)
            if a_avg_diff > 0.01:
                print(f"   ❌ 问题1: A帧平均差值过大 ({a_avg_diff:.6f}秒)，应该接近0")
                print(f"       A帧不是正常显示，而是被延迟显示")
            else:
                print(f"   ✅ A帧时间戳正常")
        
        # 检查B帧和C帧的差值模式
        if b_frames and c_frames:
            b_avg_diff = sum(t['pts_dts_diff'] for t in b_frames) / len(b_frames)
            c_avg_diff = sum(t['pts_dts_diff'] for t in c_frames) / len(c_frames)
            
            if abs(b_avg_diff - c_avg_diff) < 0.001:
                print(f"   ❌ 问题2: B帧和C帧差值相同 ({b_avg_diff:.6f}秒)，无法区分")
                print(f"       应该有不同的差值来实现不同的隐藏效果")
            else:
                print(f"   ✅ B帧和C帧有不同的时间戳差值")
        
        # 检查时间戳是否符合预期的ABC模式
        print()
        print("📊 时间戳模式总结:")
        
        if a_frames and b_frames and c_frames:
            a_avg = sum(t['pts_dts_diff'] for t in a_frames) / len(a_frames)
            b_avg = sum(t['pts_dts_diff'] for t in b_frames) / len(b_frames)
            c_avg = sum(t['pts_dts_diff'] for t in c_frames) / len(c_frames)
            
            print(f"   实际模式: A帧差值={a_avg:.6f}, B帧差值={b_avg:.6f}, C帧差值={c_avg:.6f}")
            print(f"   预期模式: A帧差值≈0, B帧差值>0, C帧差值>0且≠B帧")
            
            # 判断是否符合预期
            if a_avg < 0.01 and b_avg > 0.01 and c_avg > 0.001 and abs(b_avg - c_avg) > 0.001:
                print("   ✅ 时间戳隐藏技术正确实现")
            else:
                print("   ❌ 时间戳隐藏技术实现有问题")
                
                if a_avg >= 0.01:
                    print(f"      - A帧差值过大，不是正常显示")
                if b_avg <= 0.01:
                    print(f"      - B帧差值过小，可能没有被隐藏")
                if c_avg <= 0.001:
                    print(f"      - C帧差值过小，可能没有被隐藏")
                if abs(b_avg - c_avg) <= 0.001:
                    print(f"      - B帧和C帧差值相同，无法区分隐藏效果")
        
        print()
        print("=" * 80)
        print("详细分析完成")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 详细时间戳分析失败: {e}")

if __name__ == "__main__":
    video_path = r"C:\Users\<USER>\Desktop\下载的\atell.mp4"
    detailed_timestamp_analysis(video_path)
