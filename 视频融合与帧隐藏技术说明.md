# 视频融合与帧隐藏技术实现说明

## 功能概述

本程序实现了A视频与B视频的融合，通过将B视频帧转换为B帧并使用负时间戳技术来隐藏B视频帧，同时保持A视频帧的正常播放和显示。

## 技术原理

### 1. B帧转换技术
- **目标**: 将B视频的所有帧转换为B帧（双向预测帧）
- **实现**: 使用FFmpeg的libx264编码器，配置特定参数强制生成B帧
- **关键参数**:
  - `-bf 16`: 设置最大B帧数量
  - `-b_strategy 2`: 使用最优B帧策略
  - `-flags +cgop`: 关闭GOP（Group of Pictures）
  - `-g 250`: 设置GOP大小
  - `-sc_threshold 40`: 场景切换检测阈值

### 2. 负时间戳隐藏技术
- **原理**: 通过设置负的PTS（Presentation Time Stamp）和DTS（Decoding Time Stamp）值来隐藏帧
- **实现**: 使用FFmpeg的setpts滤镜，对前3帧B视频帧设置负时间戳
- **关键技术**:
  ```bash
  setpts=if(lt(n\,3)\,PTS-2000000\,PTS)
  ```
  - `lt(n\,3)`: 判断帧序号是否小于3
  - `PTS-2000000`: 将PTS减去2秒（2,000,000微秒）
  - 负时间戳的帧在播放时会被隐藏

### 3. 视频融合技术
- **方法**: 使用FFmpeg的concat滤镜将A视频和处理后的B视频连接
- **时间戳处理**: 保持A视频的正常时间戳，B视频使用负时间戳
- **音频处理**: 保留A视频的音频轨道

## 实现流程

### 第一阶段：视频分析
1. 检查FFmpeg可用性
2. 获取A视频和B视频的基本信息（时长、分辨率）
3. 验证视频文件的有效性

### 第二阶段：B视频处理
1. 创建临时工作目录
2. 将B视频转换为B帧格式
3. 设置前3帧的负时间戳（PTS-2000000微秒）
4. 配置微秒级时间精度（1,000,000 timescale）

### 第三阶段：视频融合
1. 使用concat滤镜连接A视频和处理后的B视频
2. 保持负时间戳设置（`-avoid_negative_ts disabled`）
3. 生成最终输出文件

## 关键技术参数

### FFmpeg编码参数
```bash
-c:v libx264                    # 使用H.264编码器
-bf 16                          # 最大B帧数
-b_strategy 2                   # B帧策略
-flags +cgop                    # 关闭GOP
-avoid_negative_ts disabled     # 允许负时间戳
-fflags +genpts                 # 生成PTS
-video_track_timescale 1000000  # 微秒级精度
```

### 滤镜链配置
```bash
[0:v][1:v]concat=n=2:v=1:a=0[vout]  # 连接两个视频流
settb=AVTB,setpts=if(lt(n\,3)\,PTS-2000000\,PTS)  # 负时间戳设置
```

## 预期效果

### A视频帧
- **状态**: 正常播放和显示
- **时间戳**: 保持原始PTS/DTS值
- **可见性**: 完全可见

### B视频帧
- **状态**: 被隐藏（前3帧）
- **时间戳**: 负PTS/DTS值
- **可见性**: 隐藏状态，不在播放时显示

### 融合结果
- **播放**: A视频正常播放
- **隐藏内容**: B视频帧存在于文件中但不可见
- **文件完整性**: 包含两个视频的所有数据

## 技术优势

1. **无损隐藏**: B视频帧完整保存在文件中
2. **播放兼容**: A视频正常播放，用户体验不受影响
3. **精确控制**: 微秒级时间戳精度控制
4. **标准兼容**: 使用标准H.264编码，兼容性良好

## 使用说明

1. 运行程序：`python sjtest.py`
2. 选择视频A文件（主要播放内容）
3. 选择视频B文件（需要隐藏的内容）
4. 程序自动处理并生成融合视频
5. 输出文件命名格式：`[视频A名称]tell.mp4`

## 注意事项

1. **FFmpeg依赖**: 需要系统安装FFmpeg并配置PATH
2. **处理时间**: 根据视频大小和复杂度，处理时间可能较长
3. **临时文件**: 程序会创建临时文件，处理完成后自动清理
4. **音频处理**: 默认使用A视频的音频轨道
5. **格式支持**: 支持常见视频格式（MP4、AVI、MOV等）

## 技术参考

- FFmpeg官方文档：https://ffmpeg.org/documentation.html
- H.264编码标准：ITU-T H.264/MPEG-4 AVC
- 时间戳操作：FFmpeg setpts滤镜文档
- B帧技术：MPEG视频压缩标准
