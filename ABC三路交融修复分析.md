# ABC三路交融逻辑错误分析与修复方案

## 问题分析总结

### 🚨 发现的关键问题

1. **帧率计算根本错误**
   - 当前：750帧在25秒播放 = 30fps
   - 正确：750帧应该在8.33秒播放 = 90fps (3倍帧率)
   - 原因：ABC三路交融需要3倍帧率才能在相同时长内隐藏BC帧

2. **时间戳隐藏完全失效**
   - 当前：所有帧PTS=DTS，没有时间戳分离
   - 正确：BC帧应该有50/100纳秒偏移
   - 原因：简化处理移除了时间戳偏移功能

3. **视频时长错误**
   - 当前：输出25秒（错误）
   - 正确：应该是8.33秒（A视频时长）
   - 原因：帧率设置错误导致时长计算错误

4. **ABC序列逻辑错误**
   - 当前：750帧使用标准30fps播放
   - 正确：750帧需要90fps播放，每3帧为一组ABC

## 时间戳验证结果

### 实际测试数据
```
A视频：250帧，8.33秒，30fps
输出视频：750帧，25秒，实际15fps（错误）
应该是：750帧，8.33秒，90fps（正确）
```

### 时间戳分析
```
前50帧分析：
- 帧间隔：66.667ms（标准30fps间隔）
- PTS=DTS：没有时间戳分离
- 偏移量：0（应该有50/100纳秒偏移）
- ABC模式：未检测到
```

## 修复方案

### 1. 帧率计算修复
```python
# 修复前
fps = 30  # 错误：使用A视频帧率
cmd = ['-r', str(fps)]  # 30fps

# 修复后  
abc_fps = fps * 3  # 正确：ABC三路交融需要3倍帧率
cmd = ['-r', str(abc_fps)]  # 90fps
```

### 2. 时间戳偏移修复
```python
# 修复前（简化处理，无偏移）
cmd = ['ffmpeg', '-i', input, '-c:v', 'libx264']

# 修复后（恢复时间戳偏移）
base_interval = int(2000000000 / fps)  # A视频基础间隔
cmd = [
    'ffmpeg', '-i', input,
    '-vf', f'settb=AVTB,setpts=if(eq(mod(N\\,3)\\,0)\\,floor(N/3)*{base_interval}\\,if(eq(mod(N\\,3)\\,1)\\,floor(N/3)*{base_interval}+50\\,floor(N/3)*{base_interval}+100))',
    '-r', str(abc_fps)
]
```

### 3. 视频时长修复
```python
# 修复前
# 750帧 ÷ 30fps = 25秒（错误）

# 修复后
# 750帧 ÷ 90fps = 8.33秒（正确，与A视频时长一致）
```

## 预期修复效果

### 修复后的时间戳模式
```
帧序号 | 类型 | PTS时间戳 | 偏移量 | 说明
0      | A    | 0         | 0      | 正常显示
1      | B    | 50        | +50ns  | 隐藏
2      | C    | 100       | +100ns | 隐藏
3      | A    | 333333333 | 0      | 正常显示
4      | B    | 333333383 | +50ns  | 隐藏
5      | C    | 333333433 | +100ns | 隐藏
...
```

### 修复后的播放效果
- **视频时长**：8.33秒（与A视频一致）
- **显示内容**：只显示A视频帧（每3帧中的第1帧）
- **隐藏效果**：BC帧被时间戳偏移隐藏
- **帧率**：90fps（3倍帧率实现快速切换）

## 核心修复原理

### ABC三路交融的正确逻辑
1. **帧序列**：A1-B1-C1-A2-B2-C2-A3-B3-C3...
2. **时间戳**：A帧正常，BC帧微小偏移
3. **帧率**：3倍原始帧率（90fps vs 30fps）
4. **播放效果**：只显示A帧，BC帧被隐藏

### 时间戳隐藏机制
- **A帧**：PTS=DTS=正常时间戳，播放器正常显示
- **B帧**：PTS=A帧+50ns，DTS=正常，播放器跳过
- **C帧**：PTS=A帧+100ns，DTS=正常，播放器跳过

## 修复步骤

1. 修正帧率计算：使用3倍帧率
2. 恢复时间戳偏移：实现50/100纳秒偏移
3. 修正视频时长：保持A视频时长不变
4. 验证ABC序列：确保时间戳模式正确

修复后将实现真正的ABC三路交融时间戳隐藏技术。
